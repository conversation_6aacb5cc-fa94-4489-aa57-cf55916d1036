import jwt from 'jsonwebtoken';

// Test JWT verification with the same secret as the API
const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key-here';
const token = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************************************************************************************************************.EUAjVV0CYqTEIVY85QqQYp1L-3blrVVPetigmOccQGk';

console.log('Testing JWT verification...');
console.log('JWT_SECRET:', JWT_SECRET);
console.log('Token:', token);

try {
  const decoded = jwt.verify(token, JWT_SECRET);
  console.log('✅ Token verification successful:');
  console.log(decoded);
  console.log('Has userId:', !!decoded.userId);
  console.log('Has id:', !!decoded.id);
} catch (error) {
  console.error('❌ Token verification failed:', error.message);
}
