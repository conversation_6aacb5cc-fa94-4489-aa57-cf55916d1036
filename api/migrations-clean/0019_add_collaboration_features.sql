-- Migration 0019: Add Collaboration Features
-- Description: Add collaboration_sessions table and collaborative editing columns to case_notes
-- Author: System
-- Date: 2025-09-11

\echo 'Starting migration 0019: Add collaboration features...'

-- Create collaboration_sessions table for tracking active collaborative editing sessions
CREATE TABLE IF NOT EXISTS collaboration_sessions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    case_id UUID NOT NULL REFERENCES cases(id) ON DELETE CASCADE,
    note_type_id UUID NOT NULL REFERENCES note_types(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    session_start TIMESTAMP NOT NULL DEFAULT NOW(),
    session_end TIMESTAMP,
    is_active BOOLEAN NOT NULL DEFAULT true,
    cursor_position JSONB,
    user_color VARCHAR(7) -- Hex color for user identification in collaborative editor
);

-- Create indexes for collaboration_sessions
CREATE INDEX IF NOT EXISTS idx_collaboration_active 
ON collaboration_sessions(case_id, note_type_id, is_active);

-- Create unique constraint to prevent duplicate active sessions
ALTER TABLE collaboration_sessions 
ADD CONSTRAINT unique_active_session 
UNIQUE (case_id, note_type_id, user_id, is_active);

-- Add collaborative editing columns to case_notes table
ALTER TABLE case_notes 
ADD COLUMN IF NOT EXISTS yjs_document TEXT,
ADD COLUMN IF NOT EXISTS canvas_blocks JSONB DEFAULT '[]'::jsonb,
ADD COLUMN IF NOT EXISTS medical_codes JSONB DEFAULT '{}'::jsonb,
ADD COLUMN IF NOT EXISTS active_editors JSONB DEFAULT '[]'::jsonb,
ADD COLUMN IF NOT EXISTS document_version INTEGER DEFAULT 1,
ADD COLUMN IF NOT EXISTS collaboration_enabled BOOLEAN DEFAULT true;

-- Add comments for documentation
COMMENT ON TABLE collaboration_sessions IS 'Tracks active collaborative editing sessions for real-time note editing';
COMMENT ON COLUMN collaboration_sessions.case_id IS 'Reference to the case being edited';
COMMENT ON COLUMN collaboration_sessions.note_type_id IS 'Reference to the type of note being edited';
COMMENT ON COLUMN collaboration_sessions.user_id IS 'Reference to the user participating in the session';
COMMENT ON COLUMN collaboration_sessions.session_start IS 'When the collaborative session started';
COMMENT ON COLUMN collaboration_sessions.session_end IS 'When the collaborative session ended';
COMMENT ON COLUMN collaboration_sessions.is_active IS 'Whether the session is currently active';
COMMENT ON COLUMN collaboration_sessions.cursor_position IS 'JSON data storing the user cursor position in the document';
COMMENT ON COLUMN collaboration_sessions.user_color IS 'Hex color code for identifying the user in the collaborative editor';

COMMENT ON COLUMN case_notes.yjs_document IS 'Yjs document data stored as base64 encoded binary for collaborative editing';
COMMENT ON COLUMN case_notes.canvas_blocks IS 'JSON array of canvas/block-based content for rich text editing';
COMMENT ON COLUMN case_notes.medical_codes IS 'JSON object storing medical codes and terminology references';
COMMENT ON COLUMN case_notes.active_editors IS 'JSON array of users currently editing this note';
COMMENT ON COLUMN case_notes.document_version IS 'Version number for document versioning and conflict resolution';
COMMENT ON COLUMN case_notes.collaboration_enabled IS 'Whether collaborative editing is enabled for this note';

\echo 'Migration 0019 completed: Added collaboration features'
