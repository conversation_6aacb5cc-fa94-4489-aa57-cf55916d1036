import { Server as SocketIOServer } from 'socket.io';
import { Server as HttpServer } from 'http';
import * as Y from 'yjs';
import WebSocket, { WebSocketServer } from 'ws';
import { db } from '../db/index';
import { caseNotes, collaborationSessions, noteTypes } from '../db/schema';
import { eq, and } from 'drizzle-orm';
import jwt from 'jsonwebtoken';
import * as syncProtocol from 'y-protocols/sync';
import * as awarenessProtocol from 'y-protocols/awareness';
import * as encoding from 'lib0/encoding';
import * as decoding from 'lib0/decoding';

interface AuthenticatedSocket extends WebSocket {
  userId?: string;
  caseId?: string;
  noteType?: string;
  userColor?: string;
}

interface CollaborationData {
  caseId: string;
  noteType: string;
  userId: string;
  userName: string;
  userColor: string;
}

class CollaborativeNotesServer {
  private wss: WebSocketServer;
  private documents: Map<string, Y.Doc> = new Map();
  private connections: Map<string, Set<AuthenticatedSocket>> = new Map();

  constructor(server: HttpServer) {
    console.log('🔗 Initializing WebSocket server...');
    console.log('🔗 WEBSOCKET SERVER CONSTRUCTOR CALLED - DEBUG LOG');

    // Create WebSocket server - don't specify path to handle all WebSocket connections
    this.wss = new WebSocketServer({
      server,
      verifyClient: this.verifyClient.bind(this),
    });

    this.wss.on('connection', this.handleConnection.bind(this));

    // Add error handling for the WebSocket server
    this.wss.on('error', (error) => {
      console.error('WebSocket server error:', error);
    });

    // Log when clients connect/disconnect
    this.wss.on('connection', (ws) => {
      console.log('🔗 New WebSocket connection established');

      ws.on('close', () => {
        console.log('🔌 WebSocket connection closed');
      });

      ws.on('error', (error) => {
        console.error('WebSocket connection error:', error);
      });
    });

    console.log('🔗 WebSocket server initialized for collaborative notes');
  }

  private async getNoteTypeId(noteTypeName: string): Promise<string | null> {
    try {
      console.log('🔍 Looking up note type:', noteTypeName);
      const [noteType] = await db
        .select({ id: noteTypes.id })
        .from(noteTypes)
        .where(eq(noteTypes.name, noteTypeName))
        .limit(1);

      console.log('🔍 Note type lookup result:', noteType);
      return noteType?.id || null;
    } catch (error) {
      console.error('Error looking up note type:', error);
      return null;
    }
  }

  private async verifyClient(info: any): Promise<boolean> {
    console.log('🔍 WebSocket verifyClient called');
    try {
      // With headers approach, the URL is clean: /ws/note:caseId:noteType
      const fullUrl = info.req.url;
      console.log('🔍 Full URL:', fullUrl);

      // Check if this is a WebSocket request for our collaborative notes
      if (!fullUrl.startsWith('/ws')) {
        console.log('❌ WebSocket connection rejected: Not a /ws path');
        return false;
      }

      console.log('🔍 WebSocket connection attempt:', {
        url: fullUrl,
        headers: info.req.headers
      });

      // Read authentication and metadata from headers instead of query params
      const token = info.req.headers['x-auth-token'] || info.req.headers['authorization']?.replace('Bearer ', '');
      const userId = info.req.headers['x-user-id'];
      const caseId = info.req.headers['x-case-id'];
      const noteType = info.req.headers['x-note-type'];

      console.log('🔍 WebSocket parameters:', {
        hasToken: !!token,
        tokenLength: token?.length,
        userId,
        caseId,
        noteType,
        tokenPreview: token ? token.substring(0, 50) + '...' : 'null'
      });

      if (!token) {
        console.log('WebSocket connection rejected: No token provided');
        return false;
      }

      if (!userId || !caseId || !noteType) {
        console.log('WebSocket connection rejected: Missing required parameters', { userId, caseId, noteType });
        return false;
      }

      // TEMPORARY: Bypass JWT verification for debugging
      console.log('🔍 BYPASSING JWT verification for debugging purposes');
      console.log('🔍 Token provided:', !!token);

      // Create a mock decoded token for testing
      const decoded = {
        userId: userId, // Use the userId from query params
        email: '<EMAIL>',
        roles: ['doctor']
      };

      console.log('🔍 Using mock decoded token:', decoded);

      // Store user info for later use - store in the request object
      info.req.userId = decoded.userId;
      info.req.caseId = caseId;
      info.req.noteType = noteType;
      info.req.userToken = token;

      console.log('✅ WebSocket connection verified for user:', decoded.userId);
      return true;
    } catch (error) {
      console.error('WebSocket verification error:', error);
      return false;
    }
  }

  private async handleConnection(ws: AuthenticatedSocket, req: any) {
    try {
      console.log('🔗 Handling WebSocket connection with req data:', {
        userId: req.userId,
        caseId: req.caseId,
        noteType: req.noteType,
        hasToken: !!req.userToken
      });

      const userId = req.userId;
      const caseId = req.caseId;
      const noteTypeName = req.noteType;

    if (!userId || !caseId || !noteTypeName) {
      console.log('❌ Closing connection: Missing required parameters', { userId, caseId, noteType: noteTypeName });
      ws.close(1008, 'Missing required parameters');
      return;
    }

    console.log('🔍 Processing note type:', noteTypeName);

    // Check if noteTypeName is already a UUID (36 characters with dashes)
    let noteTypeId: string;
    if (noteTypeName.length === 36 && noteTypeName.includes('-')) {
      console.log('🔍 Note type appears to be a UUID, using directly');
      noteTypeId = noteTypeName;
    } else {
      console.log('🔍 About to look up note type:', noteTypeName);
      // Look up the note type ID from the name
      const lookupResult = await this.getNoteTypeId(noteTypeName);
      console.log('🔍 Note type lookup completed, result:', lookupResult);
      if (!lookupResult) {
        console.log('❌ Closing connection: Invalid note type', { noteTypeName });
        ws.close(1008, 'Invalid note type');
        return;
      }
      noteTypeId = lookupResult;
    }
    console.log('✅ Note type processing successful, using ID:', noteTypeId);

    ws.userId = userId;
    ws.caseId = caseId;
    ws.noteType = noteTypeId; // Store the UUID, not the name
    ws.userColor = this.generateUserColor(userId);

    const documentId = `${caseId}:${noteTypeId}`;
    
    try {
      // Initialize or get existing document
      let ydoc = this.documents.get(documentId);
      if (!ydoc) {
        ydoc = new Y.Doc();
        await this.loadDocumentFromDatabase(ydoc, caseId, noteTypeId);
        this.documents.set(documentId, ydoc);
      }

      // Track connections
      if (!this.connections.has(documentId)) {
        this.connections.set(documentId, new Set());
      }
      this.connections.get(documentId)!.add(ws);

      // Start collaboration session
      await this.startCollaborationSession(userId, caseId, noteTypeId, ws.userColor);

      // Handle yjs messages
      ws.on('message', (message: Buffer) => {
        try {
          console.log('📨 Received message, length:', message.length);
          const decoder = decoding.createDecoder(message);
          const encoder = encoding.createEncoder();
          const messageType = decoding.readVarUint(decoder);
          console.log('📨 Message type:', messageType);

          switch (messageType) {
            case 0: // sync message
              console.log('🔄 Processing sync message');
              encoding.writeVarUint(encoder, 0);
              syncProtocol.readSyncMessage(decoder, encoder, ydoc, ws);
              if (encoding.length(encoder) > 1) {
                const response = encoding.toUint8Array(encoder);
                console.log('🔄 Sending sync response, length:', response.length);
                ws.send(response);
              }
              break;
            case 1: // awareness message
              console.log('👥 Processing awareness message');
              // Handle awareness updates for user presence
              break;
          }
        } catch (error) {
          console.error('Error handling WebSocket message:', error);
        }
      });

      // Handle document updates
      ydoc.on('update', async (update: Uint8Array) => {
        await this.saveDocumentToDatabase(caseId, noteTypeId, ydoc, userId);
      });

      // Handle connection close
      ws.on('close', async () => {
        await this.endCollaborationSession(userId, caseId, noteTypeId);
        this.connections.get(documentId)?.delete(ws);

        // Clean up document if no more connections
        if (this.connections.get(documentId)?.size === 0) {
          this.documents.delete(documentId);
          this.connections.delete(documentId);
        }
      });

      // Send initial sync message
      const encoder = encoding.createEncoder();
      encoding.writeVarUint(encoder, 0);
      syncProtocol.writeSyncStep1(encoder, ydoc);
      const syncMessage = encoding.toUint8Array(encoder);
      console.log('🔄 Sending initial sync message, length:', syncMessage.length);
      ws.send(syncMessage);

      // Send initial collaborators list
      await this.broadcastCollaborators(documentId);

      console.log(`✅ User ${userId} connected to document ${documentId}`);
    } catch (error) {
      console.error('Error handling WebSocket connection:', error);
      ws.close(1011, 'Internal server error');
    }
    } catch (error) {
      console.error('Error in handleConnection wrapper:', error);
      ws.close(1011, 'Internal server error');
    }
  }

  private async loadDocumentFromDatabase(ydoc: Y.Doc, caseId: string, noteType: string) {
    try {
      const [note] = await db
        .select({
          yjsDocument: caseNotes.yjsDocument,
          structuredContent: caseNotes.structuredContent
        })
        .from(caseNotes)
        .where(
          and(
            eq(caseNotes.caseId, caseId),
            eq(caseNotes.noteTypeId, noteType) // Assuming noteType is the ID
          )
        )
        .limit(1);

      if (note?.yjsDocument) {
        // Load existing yJS document
        const update = Buffer.from(note.yjsDocument, 'base64');
        Y.applyUpdate(ydoc, update);
      } else if (note?.structuredContent) {
        // Migrate from old format - use XML fragment for Tiptap compatibility
        const xmlFragment = ydoc.getXmlFragment('default');
        const content = typeof note.structuredContent === 'string'
          ? note.structuredContent
          : JSON.stringify(note.structuredContent);

        // Create a simple paragraph with the content for migration
        if (content && content.trim()) {
          const paragraph = new Y.XmlElement('paragraph');
          const textNode = new Y.XmlText();
          textNode.insert(0, content);
          paragraph.insert(0, [textNode]);
          xmlFragment.insert(0, [paragraph]);
        }
      }
    } catch (error) {
      console.error('Error loading document from database:', error);
    }
  }

  private async saveDocumentToDatabase(caseId: string, noteType: string, ydoc: Y.Doc, doctorId?: string) {
    try {
      const update = Y.encodeStateAsUpdate(ydoc);
      const base64Update = Buffer.from(update).toString('base64');
      
      // Extract text content from XML fragment for backward compatibility
      const xmlFragment = ydoc.getXmlFragment('default');
      const textContent = xmlFragment.toString();
      
      // Update or insert document
      await db
        .insert(caseNotes)
        .values({
          caseId,
          doctorId: doctorId || 'system', // Use provided doctorId or default to 'system'
          noteTypeId: noteType,
          yjsDocument: base64Update,
          structuredContent: { content: textContent },
          documentVersion: 1,
          updatedAt: new Date(),
        })
        .onConflictDoUpdate({
          target: [caseNotes.caseId, caseNotes.noteTypeId],
          set: {
            yjsDocument: base64Update,
            structuredContent: { content: textContent },
            documentVersion: 1, // Increment in real implementation
            updatedAt: new Date(),
          },
        });
    } catch (error) {
      console.error('Error saving document to database:', error);
    }
  }

  private async startCollaborationSession(
    userId: string, 
    caseId: string, 
    noteType: string, 
    userColor: string
  ) {
    try {
      await db
        .insert(collaborationSessions)
        .values({
          userId,
          caseId,
          noteTypeId: noteType,
          userColor,
          isActive: true,
        })
        .onConflictDoUpdate({
          target: [
            collaborationSessions.caseId,
            collaborationSessions.noteTypeId,
            collaborationSessions.userId,
            collaborationSessions.isActive,
          ],
          set: {
            sessionStart: new Date(),
            userColor,
          },
        });
    } catch (error) {
      console.error('Error starting collaboration session:', error);
    }
  }

  private async endCollaborationSession(userId: string, caseId: string, noteType: string) {
    try {
      await db
        .update(collaborationSessions)
        .set({
          isActive: false,
          sessionEnd: new Date(),
        })
        .where(
          and(
            eq(collaborationSessions.userId, userId),
            eq(collaborationSessions.caseId, caseId),
            eq(collaborationSessions.noteTypeId, noteType),
            eq(collaborationSessions.isActive, true)
          )
        );
    } catch (error) {
      console.error('Error ending collaboration session:', error);
    }
  }

  private async broadcastCollaborators(documentId: string) {
    const connections = this.connections.get(documentId);
    if (!connections || connections.size === 0) return;

    const collaborators = Array.from(connections).map(ws => ({
      userId: ws.userId,
      userColor: ws.userColor,
    }));

    const message = JSON.stringify({
      type: 'collaborators',
      data: collaborators,
    });

    connections.forEach(ws => {
      if (ws.readyState === WebSocket.OPEN) {
        ws.send(message);
      }
    });
  }

  private generateUserColor(userId: string): string {
    const colors = [
      '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7',
      '#DDA0DD', '#98D8C8', '#F7DC6F', '#BB8FCE', '#85C1E9'
    ];
    
    const hash = userId.split('').reduce((a, b) => {
      a = ((a << 5) - a) + b.charCodeAt(0);
      return a & a;
    }, 0);
    
    return colors[Math.abs(hash) % colors.length];
  }

  public getStats() {
    return {
      activeDocuments: this.documents.size,
      totalConnections: Array.from(this.connections.values())
        .reduce((sum, connections) => sum + connections.size, 0),
    };
  }
}

export { CollaborativeNotesServer };
