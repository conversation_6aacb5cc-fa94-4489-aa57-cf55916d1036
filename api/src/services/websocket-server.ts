import { Server as HttpServer } from 'http';
import * as Y from 'yjs';
import WebSocket, { WebSocketServer } from 'ws';
import { db } from '../db/index';
import { caseNotes, collaborationSessions, noteTypes } from '../db/schema';
import { eq, and } from 'drizzle-orm';
import * as syncProtocol from 'y-protocols/sync';
import * as awarenessProtocol from 'y-protocols/awareness';
import * as encoding from 'lib0/encoding';
import * as decoding from 'lib0/decoding';
import { logger } from '../utils/structuredLogger';
import { WebSocketAuthMiddleware } from '../middleware/websocketAuth';

interface AuthenticatedSocket extends WebSocket {
  userId?: string;
  caseId?: string;
  noteType?: string;
  userColor?: string;
}

interface CollaborationData {
  caseId: string;
  noteType: string;
  userId: string;
  userName: string;
  userColor: string;
}

class CollaborativeNotesServer {
  private wss: WebSocketServer;
  private documents: Map<string, Y.Doc> = new Map();
  private connections: Map<string, Set<AuthenticatedSocket>> = new Map();

  constructor(server: HttpServer) {
    logger.info('🔗 Initializing WebSocket server...');

    // Create WebSocket server with enhanced authentication
    this.wss = new WebSocketServer({
      server,
      verifyClient: this.verifyClient.bind(this),
    });

    this.wss.on('connection', this.handleConnection.bind(this));

    // Add error handling for the WebSocket server
    this.wss.on('error', (error) => {
      logger.error('WebSocket server error', { error: (error as Error).message });
    });

    // Log when clients connect/disconnect
    this.wss.on('connection', (ws) => {
      logger.info('🔗 New WebSocket connection established');

      ws.on('close', () => {
        logger.info('🔌 WebSocket connection closed');
      });

      ws.on('error', (error) => {
        logger.error('WebSocket connection error', { error: (error as Error).message });
      });
    });

    logger.info('🔗 WebSocket server initialized for collaborative notes');
  }

  private async getNoteTypeId(noteTypeName: string): Promise<string | null> {
    try {
      console.log('🔍 Looking up note type:', noteTypeName);
      const [noteType] = await db
        .select({ id: noteTypes.id })
        .from(noteTypes)
        .where(eq(noteTypes.name, noteTypeName))
        .limit(1);

      console.log('🔍 Note type lookup result:', noteType);
      return noteType?.id || null;
    } catch (error) {
      console.error('Error looking up note type:', error);
      return null;
    }
  }

  private async verifyClient(info: any): Promise<boolean> {
    // Use the enhanced WebSocket authentication middleware
    return await WebSocketAuthMiddleware.verifyClient({
      origin: info.origin || '',
      secure: info.secure || false,
      req: info.req,
    });
  }

  private async handleConnection(ws: AuthenticatedSocket, req: any) {
    try {
      logger.info('🔗 Handling WebSocket connection', {
        userId: req.userId,
        caseId: req.caseId,
        noteType: req.noteType,
        hasToken: !!req.userToken,
        userEmail: req.user?.email,
        userRoles: req.user?.roles,
      });

      const userId = req.userId;
      const caseId = req.caseId;
      const noteTypeName = req.noteType;

      if (!userId || !caseId || !noteTypeName) {
        logger.warn('❌ Closing connection: Missing required parameters', {
          userId,
          caseId,
          noteType: noteTypeName
        });
        ws.close(1008, 'Missing required parameters');
        return;
      }

      logger.info('🔍 Processing note type', { noteTypeName });

      // Check if noteTypeName is already a UUID (36 characters with dashes)
      let noteTypeId: string;
      if (noteTypeName.length === 36 && noteTypeName.includes('-')) {
        logger.info('🔍 Note type appears to be a UUID, using directly');
        noteTypeId = noteTypeName;
      } else {
        logger.info('🔍 About to look up note type', { noteTypeName });
        // Look up the note type ID from the name
        const lookupResult = await this.getNoteTypeId(noteTypeName);
        logger.info('🔍 Note type lookup completed', { lookupResult });
        if (!lookupResult) {
          logger.warn('❌ Closing connection: Invalid note type', { noteTypeName });
          ws.close(1008, 'Invalid note type');
          return;
        }
        noteTypeId = lookupResult;
      }
      logger.info('✅ Note type processing successful', { noteTypeId });

    ws.userId = userId;
    ws.caseId = caseId;
    ws.noteType = noteTypeId; // Store the UUID, not the name
    ws.userColor = this.generateUserColor(userId);

    const documentId = `${caseId}:${noteTypeId}`;
    
    try {
      // Initialize or get existing document
      let ydoc = this.documents.get(documentId);
      if (!ydoc) {
        ydoc = new Y.Doc();
        await this.loadDocumentFromDatabase(ydoc, caseId, noteTypeId);
        this.documents.set(documentId, ydoc);
      }

      // Track connections
      if (!this.connections.has(documentId)) {
        this.connections.set(documentId, new Set());
      }
      this.connections.get(documentId)!.add(ws);

      // Start collaboration session
      await this.startCollaborationSession(userId, caseId, noteTypeId, ws.userColor);

      // Handle yjs messages
      ws.on('message', (message: Buffer) => {
        try {
          logger.debug('📨 Received message', { messageLength: message.length });
          const decoder = decoding.createDecoder(message);
          const encoder = encoding.createEncoder();
          const messageType = decoding.readVarUint(decoder);
          logger.debug('📨 Message type', { messageType });

          switch (messageType) {
            case 0: // sync message
              logger.debug('🔄 Processing sync message');
              encoding.writeVarUint(encoder, 0);
              syncProtocol.readSyncMessage(decoder, encoder, ydoc, ws);
              if (encoding.length(encoder) > 1) {
                const response = encoding.toUint8Array(encoder);
                logger.debug('🔄 Sending sync response', { responseLength: response.length });
                ws.send(response);
              }
              break;
            case 1: // awareness message
              logger.debug('👥 Processing awareness message');
              // Handle awareness updates for user presence
              break;
          }
        } catch (error) {
          logger.error('Error handling WebSocket message: ' + (error instanceof Error ? error.message : String(error)));
        }
      });

      // Handle document updates
      ydoc.on('update', async (_update: Uint8Array) => {
        await this.saveDocumentToDatabase(caseId, noteTypeId, ydoc, userId);
      });

      // Handle connection close
      ws.on('close', async () => {
        await this.endCollaborationSession(userId, caseId, noteTypeId);
        this.connections.get(documentId)?.delete(ws);

        // Clean up document if no more connections
        if (this.connections.get(documentId)?.size === 0) {
          this.documents.delete(documentId);
          this.connections.delete(documentId);
        }
      });

      // Send initial sync message
      const encoder = encoding.createEncoder();
      encoding.writeVarUint(encoder, 0);
      syncProtocol.writeSyncStep1(encoder, ydoc);
      const syncMessage = encoding.toUint8Array(encoder);
      logger.info('🔄 Sending initial sync message', { syncMessageLength: syncMessage.length });
      ws.send(syncMessage);

      // Send initial collaborators list
      await this.broadcastCollaborators(documentId);

      logger.info(`✅ User connected to document`, { userId, documentId });
    } catch (error) {
      logger.error('Error handling WebSocket connection: ' + (error instanceof Error ? error.message : String(error)));
      ws.close(1011, 'Internal server error');
    }
    } catch (error) {
      logger.error('Error in handleConnection wrapper: ' + (error instanceof Error ? error.message : String(error)));
      ws.close(1011, 'Internal server error');
    }
  }

  private async loadDocumentFromDatabase(ydoc: Y.Doc, caseId: string, noteType: string) {
    try {
      const [note] = await db
        .select({
          yjsDocument: caseNotes.yjsDocument,
          structuredContent: caseNotes.structuredContent
        })
        .from(caseNotes)
        .where(
          and(
            eq(caseNotes.caseId, caseId),
            eq(caseNotes.noteTypeId, noteType) // Assuming noteType is the ID
          )
        )
        .limit(1);

      if (note?.yjsDocument) {
        // Load existing yJS document
        const update = Buffer.from(note.yjsDocument, 'base64');
        Y.applyUpdate(ydoc, update);
      } else if (note?.structuredContent) {
        // Migrate from old format - use XML fragment for Tiptap compatibility
        const xmlFragment = ydoc.getXmlFragment('default');
        const content = typeof note.structuredContent === 'string'
          ? note.structuredContent
          : JSON.stringify(note.structuredContent);

        // Create a simple paragraph with the content for migration
        if (content && content.trim()) {
          const paragraph = new Y.XmlElement('paragraph');
          const textNode = new Y.XmlText();
          textNode.insert(0, content);
          paragraph.insert(0, [textNode]);
          xmlFragment.insert(0, [paragraph]);
        }
      }
    } catch (error) {
      console.error('Error loading document from database:', error);
    }
  }

  private async saveDocumentToDatabase(caseId: string, noteType: string, ydoc: Y.Doc, doctorId?: string) {
    try {
      const update = Y.encodeStateAsUpdate(ydoc);
      const base64Update = Buffer.from(update).toString('base64');
      
      // Extract text content from XML fragment for backward compatibility
      const xmlFragment = ydoc.getXmlFragment('default');
      const textContent = xmlFragment.toString();
      
      // Update or insert document
      await db
        .insert(caseNotes)
        .values({
          caseId,
          doctorId: doctorId || 'system', // Use provided doctorId or default to 'system'
          noteTypeId: noteType,
          yjsDocument: base64Update,
          structuredContent: { content: textContent },
          documentVersion: 1,
          updatedAt: new Date(),
        })
        .onConflictDoUpdate({
          target: [caseNotes.caseId, caseNotes.noteTypeId],
          set: {
            yjsDocument: base64Update,
            structuredContent: { content: textContent },
            documentVersion: 1, // Increment in real implementation
            updatedAt: new Date(),
          },
        });
    } catch (error) {
      console.error('Error saving document to database:', error);
    }
  }

  private async startCollaborationSession(
    userId: string, 
    caseId: string, 
    noteType: string, 
    userColor: string
  ) {
    try {
      await db
        .insert(collaborationSessions)
        .values({
          userId,
          caseId,
          noteTypeId: noteType,
          userColor,
          isActive: true,
        })
        .onConflictDoUpdate({
          target: [
            collaborationSessions.caseId,
            collaborationSessions.noteTypeId,
            collaborationSessions.userId,
            collaborationSessions.isActive,
          ],
          set: {
            sessionStart: new Date(),
            userColor,
          },
        });
    } catch (error) {
      console.error('Error starting collaboration session:', error);
    }
  }

  private async endCollaborationSession(userId: string, caseId: string, noteType: string) {
    try {
      await db
        .update(collaborationSessions)
        .set({
          isActive: false,
          sessionEnd: new Date(),
        })
        .where(
          and(
            eq(collaborationSessions.userId, userId),
            eq(collaborationSessions.caseId, caseId),
            eq(collaborationSessions.noteTypeId, noteType),
            eq(collaborationSessions.isActive, true)
          )
        );
    } catch (error) {
      console.error('Error ending collaboration session:', error);
    }
  }

  private async broadcastCollaborators(documentId: string) {
    const connections = this.connections.get(documentId);
    if (!connections || connections.size === 0) return;

    const collaborators = Array.from(connections).map(ws => ({
      userId: ws.userId,
      userColor: ws.userColor,
    }));

    const message = JSON.stringify({
      type: 'collaborators',
      data: collaborators,
    });

    connections.forEach(ws => {
      if (ws.readyState === WebSocket.OPEN) {
        ws.send(message);
      }
    });
  }

  private generateUserColor(userId: string): string {
    const colors = [
      '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7',
      '#DDA0DD', '#98D8C8', '#F7DC6F', '#BB8FCE', '#85C1E9'
    ];
    
    const hash = userId.split('').reduce((a, b) => {
      a = ((a << 5) - a) + b.charCodeAt(0);
      return a & a;
    }, 0);
    
    return colors[Math.abs(hash) % colors.length];
  }

  public getStats() {
    return {
      activeDocuments: this.documents.size,
      totalConnections: Array.from(this.connections.values())
        .reduce((sum, connections) => sum + connections.size, 0),
    };
  }
}

export { CollaborativeNotesServer };
