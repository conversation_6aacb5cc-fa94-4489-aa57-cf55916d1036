import { WebSocketAuthMiddleware } from '../middleware/websocketAuth';
import jwt from 'jsonwebtoken';

// Mock dependencies
jest.mock('../db/index', () => ({
  db: {
    select: jest.fn().mockReturnThis(),
    from: jest.fn().mockReturnThis(),
    where: jest.fn().mockReturnThis(),
    limit: jest.fn().mockResolvedValue([{
      id: 'test-user-id',
      email: '<EMAIL>',
      firstName: 'Test',
      lastName: 'User',
      isActive: true,
    }]),
  },
}));

jest.mock('../db/schema/users', () => ({
  users: {
    id: 'id',
    email: 'email',
    firstName: 'firstName',
    lastName: 'lastName',
    isActive: 'isActive',
  },
}));

jest.mock('../utils/structuredLogger', () => ({
  logger: {
    info: jest.fn(),
    warn: jest.fn(),
    error: jest.fn(),
    debug: jest.fn(),
  },
}));

// Set up test environment
process.env.JWT_SECRET = 'test-secret';

describe('WebSocketAuthMiddleware', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('verifyClient', () => {
    it('should accept valid Bearer token in Authorization header', async () => {
      const token = jwt.sign(
        {
          userId: 'test-user-id',
          email: '<EMAIL>',
          roles: ['doctor'],
          sessionId: 'test-session',
        },
        'test-secret',
        { expiresIn: '1h' }
      );

      const info = {
        origin: 'http://localhost:3000',
        secure: false,
        req: {
          url: '/ws/case123/progress-note',
          headers: {
            authorization: `Bearer ${token}`,
            'x-case-id': 'case123',
            'x-note-type': 'progress-note',
          },
        },
      };

      const result = await WebSocketAuthMiddleware.verifyClient(info);
      expect(result).toBe(true);
      expect(info.req.userId).toBe('test-user-id');
      expect(info.req.caseId).toBe('case123');
      expect(info.req.noteType).toBe('progress-note');
    });

    it('should accept valid token from cookie', async () => {
      const token = jwt.sign(
        {
          userId: 'test-user-id',
          email: '<EMAIL>',
          roles: ['doctor'],
          sessionId: 'test-session',
        },
        'test-secret',
        { expiresIn: '1h' }
      );

      const info = {
        origin: 'http://localhost:3000',
        secure: false,
        req: {
          url: '/ws/case123/progress-note',
          headers: {
            cookie: `auth_token=${token}; other_cookie=value`,
            'x-case-id': 'case123',
            'x-note-type': 'progress-note',
          },
        },
      };

      const result = await WebSocketAuthMiddleware.verifyClient(info);
      expect(result).toBe(true);
      expect(info.req.userId).toBe('test-user-id');
    });

    it('should accept valid token from x-auth-token header', async () => {
      const token = jwt.sign(
        {
          userId: 'test-user-id',
          email: '<EMAIL>',
          roles: ['doctor'],
          sessionId: 'test-session',
        },
        'test-secret',
        { expiresIn: '1h' }
      );

      const info = {
        origin: 'http://localhost:3000',
        secure: false,
        req: {
          url: '/ws/case123/progress-note',
          headers: {
            'x-auth-token': token,
            'x-case-id': 'case123',
            'x-note-type': 'progress-note',
          },
        },
      };

      const result = await WebSocketAuthMiddleware.verifyClient(info);
      expect(result).toBe(true);
    });

    it('should reject connection without token', async () => {
      const info = {
        origin: 'http://localhost:3000',
        secure: false,
        req: {
          url: '/ws/case123/progress-note',
          headers: {
            'x-case-id': 'case123',
            'x-note-type': 'progress-note',
          },
        },
      };

      const result = await WebSocketAuthMiddleware.verifyClient(info);
      expect(result).toBe(false);
    });

    it('should reject connection with invalid token', async () => {
      const info = {
        origin: 'http://localhost:3000',
        secure: false,
        req: {
          url: '/ws/case123/progress-note',
          headers: {
            authorization: 'Bearer invalid-token',
            'x-case-id': 'case123',
            'x-note-type': 'progress-note',
          },
        },
      };

      const result = await WebSocketAuthMiddleware.verifyClient(info);
      expect(result).toBe(false);
    });

    it('should reject connection without required parameters', async () => {
      const token = jwt.sign(
        {
          userId: 'test-user-id',
          email: '<EMAIL>',
          roles: ['doctor'],
          sessionId: 'test-session',
        },
        'test-secret',
        { expiresIn: '1h' }
      );

      const info = {
        origin: 'http://localhost:3000',
        secure: false,
        req: {
          url: '/ws/case123/progress-note',
          headers: {
            authorization: `Bearer ${token}`,
            // Missing x-case-id and x-note-type
          },
        },
      };

      const result = await WebSocketAuthMiddleware.verifyClient(info);
      expect(result).toBe(false);
    });

    it('should reject connection to non-ws path', async () => {
      const token = jwt.sign(
        {
          userId: 'test-user-id',
          email: '<EMAIL>',
          roles: ['doctor'],
          sessionId: 'test-session',
        },
        'test-secret',
        { expiresIn: '1h' }
      );

      const info = {
        origin: 'http://localhost:3000',
        secure: false,
        req: {
          url: '/api/some-endpoint',
          headers: {
            authorization: `Bearer ${token}`,
            'x-case-id': 'case123',
            'x-note-type': 'progress-note',
          },
        },
      };

      const result = await WebSocketAuthMiddleware.verifyClient(info);
      expect(result).toBe(false);
    });

    it('should extract parameters from URL when headers are missing', async () => {
      const token = jwt.sign(
        {
          userId: 'test-user-id',
          email: '<EMAIL>',
          roles: ['doctor'],
          sessionId: 'test-session',
        },
        'test-secret',
        { expiresIn: '1h' }
      );

      const info = {
        origin: 'http://localhost:3000',
        secure: false,
        req: {
          url: '/ws/case123/progress-note?userId=test-user-id',
          headers: {
            authorization: `Bearer ${token}`,
          },
        },
      };

      const result = await WebSocketAuthMiddleware.verifyClient(info);
      expect(result).toBe(true);
      expect(info.req.caseId).toBe('case123');
      expect(info.req.noteType).toBe('progress-note');
    });
  });
});
