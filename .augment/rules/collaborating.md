---
type: "always_apply"
---

# AI Coding — General Development Rules (Cross-Project)

**Intent:** Enforce safe, minimal-change behavior from AI coding staff across all stacks and repos.

---

## 0) Golden Rules (Hard Fail if Violated)
- **NEVER** create temp files, scratch dirs, or ad-hoc scaffolding anywhere in the repo.  
- **NEVER** run database migrations directly (local or remote). Use the project’s approved pipeline/job or submit migration files only.  
- **NEVER** commit or log secrets, tokens, keys, PHI/PII, or sample data with PHI/PII.  
- **NEVER** bypass containerization. All build/test/lint tasks must run in containers.  
- **NEVER** widen scope without an explicit plan/update to the task.  
- **NEVER** reduce security posture, disable checks, or add TODOs that defer security fixes.  
- **ALWAYS** enable **OpenTelemetry (OTel)** for traces/logs/metrics. Even with *no collector*, emit locally (console) and preserve W3C context (`trace_id`, `span_id`).  

## 0.1) Exceptions to Golden Rules
- **ALWAYS** use npx on local (not in docker) to create migrations, to add new packages to registrys
- **ALWAYS** run from inside docker to actual execute tasks or builds.

---

## 1) Build & Runtime Model
- **Assume host volumes are NOT mounted.** Code changes require a **container rebuild** to take effect.  
- Only use **containerized** commands (e.g., `docker compose run --rm <svc> <cmd>`).  
- Prefer **multi-stage images**, non-root users, health checks, and `.dockerignore`.  
- Changes to build args, env vars, or deps **must** include a note in the PR body titled **“Runtime Change Notice.”**  

---

## 2) Change Discipline
- **Default posture: minimal diff.** Prefer small, reversible edits over structural rewrites.  
- **Think twice before changes** that add: new directories, global utilities, new dependencies, or new build steps.  
- **No speculative abstractions.** Only introduce interfaces/layers when required by the task.  
- **No cross-cutting edits** unless explicitly in scope.  
- If a rule blocks delivery, **stop and produce a short blocking report** with options.  

---

## 3) Files You May/Must Not Touch
- **May touch:** files explicitly referenced by the task + nearest neighbors necessary to complete it (tests, configs).  
- **Must not touch:** CI templates, infra manifests, secrets, versioning files, or policy files **unless** the task explicitly targets them.  
- **No temp artifacts:** no `.tmp`, `.bak`, `.old`, or “playground” directories; no commented-out blocks left behind.  

---

## 4) Configuration & Secrets
- Read config from environment only; **do not hardcode** values.  
- If new config is needed: add to env schema/docs; **never** provide defaults that lower security.  
- Sanitize logs; **never** print secrets, tokens, or raw payloads with sensitive data.  

---

## 5) Database & Migrations
- You may **author migration files** (following the repo’s migration tool), but **do not execute** them locally or against any environment.  
- No direct DDL via clients or scripts. No data backfills without explicit instruction.  
- If schema impacts are non-trivial, add a short **migration impact note** (lock/rollback considerations, expected runtime).  

---

## 6) Testing, Linting, and Coverage
- All tests/lint/type checks **must** run inside containers.  
- Maintain or improve existing coverage thresholds; if none are defined, **target ≥80%** on changed code.  
- Add focused tests for new logic; **no snapshot dumps** that mask behavior.  

---

## 7) Observability (OTel-First)
**Principle:** OTel is **always on**. Even without a collector, emit to console with W3C Trace Context so logs and spans correlate.

### Required Behaviors
- **Tracing:** create a root span per request/job; propagate W3C `traceparent` + `baggage`.  
- **Logging:** structure logs and always include `trace_id` and `span_id`.  
- **Metrics:** expose process/app metrics; if no collector is present, export to console.  
- **Resource attributes:** always set `service.name`, `service.version`, `deployment.environment`.  
- **Semantic conventions:** apply HTTP, DB, and messaging attributes consistently.  
- **Sampling:** default to parent-based 1.0 in dev/test; configurable in production.  

### Configuration Guidance
- By default, set the **OTel collector endpoint to `null`** (or the framework’s equivalent for “no collector”).  
- Always emit locally (console) to preserve spans/log correlation in dev/test.  
- Override the collector endpoint only via **docker-compose** or environment-specific configuration during deployment.  
- Never hardcode endpoints in code; rely on environment variables for overrides.  

---

## 8) Performance & Reliability
- Avoid N+1 queries and unbounded loops; prefer pagination and limits.  
- Respect timeouts/retries where present; **do not** increase globally without justification.  
- Any added cache or queue must come with **eviction/visibility** notes and failure handling.  

---

## 9) PR Hygiene (Definition of Done)
Include a checklist in the PR description and ensure all items pass:

- [ ] Minimal diff; no temp files; no dead/commented code.  
- [ ] All commands run in containers; rebuild performed (host not mounted).  
- [ ] No secrets in code, config, commits, or logs.  
- [ ] Tests added/updated; checks pass; coverage not reduced.  
- [ ] No direct migration execution; migrations authored only if required, with impact note.  
- [ ] Security posture unchanged or improved; no disabled linters/checks without rationale.  
- [ ] Docs updated where behavior/config changed (README or designated docs).  
- [ ] **OTel enabled**: root spans, logs correlated, default collector `null`, override only from environment.  

---

## 10) Communication & Escalation
If a constraint blocks progress, **stop** and post a concise **Decision Log** entry:

- **Problem:** (one sentence)  
- **Options:** (2–3 precise options with trade-offs)  
- **Recommendation:** (pick one; explain briefly)  
- **Rollback:** (how to revert this change)  

---

## 11) Prohibited Shortcuts (Always Reject)
- Running anything on host (npm, tests, linters, DB clients).  
- Creating playgrounds, prototypes, or “temp” structures in-repo.  
- Copy-pasting large third-party snippets without license/attribution checks.  
- Lowering encryption, authz checks, validation, or **disabling OTel**.  
- Silent dependency upgrades or lockfile churn unrelated to the task.  

---

## 12) Safe Defaults (When in Doubt)
- Make the **smallest** possible change that satisfies the task and keeps security intact.  
- Prefer **local function updates** over new shared utilities.  
- Prefer **explicit flags** and **clear docs** over hidden behavior.  
- If the project has multiple patterns, **match the prevailing style** in the edited area.  

---

### TL;DR for Agents
Build and test **only in containers** (no host).  
**No temp files.**  
**Do not run migrations**—author them if asked, but let the pipeline execute.  
**OTel is always enabled**: emit spans/logs locally by default; set collector = `null`; override only from environment configs.  
Keep diffs **small**, secure, and reversible. If something feels like tech debt or a security compromise, **stop and report** before proceeding.
