const WebSocket = require('ws');

console.log('Testing basic WebSocket connection...');

// Create WebSocket URL with authentication parameters
const baseWsUrl = 'ws://localhost:3002/ws';
const wsUrl = new URL(baseWsUrl);
wsUrl.searchParams.set('userId', '550e8400-e29b-41d4-a716-446655440002');
wsUrl.searchParams.set('caseId', '1d988dcd-f4bb-4aa1-9166-f5e2d0134945');
wsUrl.searchParams.set('noteType', 'c6867d50-6dc7-443d-aa14-061ab153d685');
wsUrl.searchParams.set('token', 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************************************************************************************************************.EUAjVV0CYqTEIVY85QqQYp1L-3blrVVPetigmOccQGk');

console.log('Connecting to:', wsUrl.toString());

// Create basic WebSocket connection
const ws = new WebSocket(wsUrl.toString());

ws.on('open', () => {
  console.log('✅ WebSocket connection opened');
  
  // Send a simple test message
  ws.send('Hello from client');
});

ws.on('message', (data) => {
  console.log('📨 Received message length:', data.length);
  console.log('📨 Received message (first 50 bytes):', data.slice(0, 50));
});

ws.on('close', (code, reason) => {
  console.log('🔌 Connection closed:', code, reason.toString());
});

ws.on('error', (error) => {
  console.log('❌ Connection error:', error.message);
});

// Keep the process alive for a few seconds to see the connection status
setTimeout(() => {
  console.log('🏁 Test completed');
  ws.close();
  process.exit(0);
}, 5000);
