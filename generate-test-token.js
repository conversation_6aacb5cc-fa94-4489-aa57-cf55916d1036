const jwt = require('jsonwebtoken');

// Use the same JWT secret as in the .env file
const JWT_SECRET = 'your-secret-key-here';

// Create a test token with the expected payload structure
const payload = {
  userId: '550e8400-e29b-41d4-a716-446655440002',
  email: '<EMAIL>',
  roles: ['doctor'],
  sessionId: 'test-session-id'
};

const token = jwt.sign(payload, JWT_SECRET, { expiresIn: '1h' });

console.log('Generated test token:');
console.log(token);

// Verify the token works
try {
  const decoded = jwt.verify(token, JWT_SECRET);
  console.log('\nToken verification successful:');
  console.log(decoded);
} catch (error) {
  console.error('Token verification failed:', error);
}
