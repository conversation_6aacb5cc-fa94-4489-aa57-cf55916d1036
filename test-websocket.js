const WebSocket = require('ws');

// Test WebSocket connection to root path to see if it's a path issue

const wsUrl = 'ws://localhost:3002/ws?userId=550e8400-e29b-41d4-a716-446655440002&caseId=aacc5a58-7a52-4c2a-aeaf-6ae1bdfea434&noteType=medical&token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************************************************************************************************************.2ohVzjCwfW2odHI1vCzkyjoQKhxHb6eODwt-PKTswaA';
console.log('Attempting to connect to:', wsUrl);

const ws = new WebSocket(wsUrl, {
  rejectUnauthorized: false // For self-signed certificates
});

ws.on('open', function open() {
  console.log('✅ WebSocket connection opened successfully!');
  
  // Send a test message
  ws.send('Hello WebSocket!');
});

ws.on('message', function message(data) {
  console.log('📨 Received message:', data.toString());
});

ws.on('error', function error(err) {
  console.error('❌ WebSocket error:', err.message);
});

ws.on('close', function close(code, reason) {
  console.log('🔌 WebSocket connection closed:', code, reason.toString());
});

// Keep the script running for a few seconds
setTimeout(() => {
  console.log('Closing connection...');
  ws.close();
}, 5000);
