const Y = require('yjs');
const { WebsocketProvider } = require('y-websocket');
const WebSocket = require('ws');

// Polyfill WebSocket for Node.js environment
global.WebSocket = WebSocket;

// Test y-websocket connection like the collaborative editor does
console.log('Testing y-websocket connection...');

// Create a new Y.Doc (like in CollabClinicalEditor)
const doc = new Y.Doc();
const documentId = 'note:1d988dcd-f4bb-4aa1-9166-f5e2d0134945:c6867d50-6dc7-443d-aa14-061ab153d685';

// WebSocket URL with parameters (like in CollabClinicalEditor)
const baseWsUrl = 'ws://localhost:3002/ws';
const wsUrl = new URL(baseWsUrl);
wsUrl.searchParams.set('userId', '550e8400-e29b-41d4-a716-446655440002');
wsUrl.searchParams.set('caseId', '1d988dcd-f4bb-4aa1-9166-f5e2d0134945');
wsUrl.searchParams.set('noteType', 'c6867d50-6dc7-443d-aa14-061ab153d685');
wsUrl.searchParams.set('token', 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************************************************************************************************************.EUAjVV0CYqTEIVY85QqQYp1L-3blrVVPetigmOccQGk');

console.log('Connecting to:', wsUrl.toString());
console.log('Document ID:', documentId);

// Create WebSocket provider (like in CollabClinicalEditor)
const provider = new WebsocketProvider(
  wsUrl.toString(),
  documentId,
  doc
);

// Set up event listeners
provider.on('status', (event) => {
  console.log('📡 Provider status:', event.status);
});

provider.on('connection-close', (event) => {
  console.log('🔌 Connection closed:', event?.code || 'unknown', event?.reason || 'no reason');
});

provider.on('connection-error', (event) => {
  console.log('❌ Connection error:', event.error);
});

provider.on('sync', (isSynced) => {
  console.log('🔄 Sync status:', isSynced ? 'synced' : 'syncing');
});

// Test the shared text type (like Tiptap uses)
const yText = doc.getText('content');

// Wait a bit to see connection status
setTimeout(() => {
  console.log('📊 Connection status after 2s:', {
    connected: provider.wsconnected,
    connecting: provider.wsconnecting,
    shouldConnect: provider.shouldConnect
  });
  
  // Try to insert some text
  yText.insert(0, 'Hello from y-websocket test!');
  console.log('📝 Inserted text, current content:', yText.toString());
  
  // Wait a bit more then cleanup
  setTimeout(() => {
    console.log('🧹 Cleaning up...');
    provider.destroy();
    process.exit(0);
  }, 2000);
}, 2000);

// Handle process termination
process.on('SIGINT', () => {
  console.log('🛑 Received SIGINT, cleaning up...');
  provider.destroy();
  process.exit(0);
});
